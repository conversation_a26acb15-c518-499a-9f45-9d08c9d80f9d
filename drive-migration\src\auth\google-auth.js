import { google } from 'googleapis';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Google Service Account Authentication với Domain-wide Delegation
 * Cho phép ứng dụng giả lập người dùng để truy cập Drive
 * Enhanced version với caching và error handling
 */
export class GoogleAuth {
    constructor() {
        this.serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
        this.privateKey = process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n');
        this.projectId = process.env.GOOGLE_PROJECT_ID;
        this.clientEmail = process.env.GOOGLE_CLIENT_EMAIL;
        this.clientId = process.env.GOOGLE_CLIENT_ID;

        // Cache cho auth clients
        this.authClientCache = new Map();
        this.tokenCache = new Map();

        // Scopes mặc định cho migration
        this.defaultScopes = [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/drive.file',
            'https://www.googleapis.com/auth/drive.metadata',
            'https://www.googleapis.com/auth/admin.directory.user.readonly'
        ];

        this.keyPath = join(__dirname, '..', '..', 'google-service-account.json');

        if (!this.serviceAccountEmail || !this.privateKey || !this.projectId) {
            throw new Error('Missing required Google Service Account credentials (GOOGLE_SERVICE_ACCOUNT_EMAIL, GOOGLE_PRIVATE_KEY, GOOGLE_PROJECT_ID)');
        }

        console.log('✅ Google Auth initialized with Service Account:', this.serviceAccountEmail);
    }

    /**
     * Tạo JWT token để giả lập người dùng
     * @param {string} userEmail - Email của người dùng cần giả lập
     * @param {string[]} scopes - Các scope cần thiết
     * @returns {string} JWT token
     */
    createJWT(userEmail, scopes = null) {
        const now = Math.floor(Date.now() / 1000);
        const scopesToUse = scopes || this.defaultScopes;

        const payload = {
            iss: this.serviceAccountEmail,
            sub: userEmail, // Giả lập người dùng này
            scope: scopesToUse.join(' '),
            aud: 'https://oauth2.googleapis.com/token',
            iat: now,
            exp: now + 3600 // Token có hiệu lực 1 giờ
        };

        return jwt.sign(payload, this.privateKey, { algorithm: 'RS256' });
    }

    /**
     * Validate Service Account credentials
     * @returns {boolean} True nếu credentials hợp lệ
     */
    validateCredentials() {
        try {
            // Test tạo JWT với dummy email
            const testJWT = this.createJWT('<EMAIL>');
            return !!testJWT;
        } catch (error) {
            console.error('❌ Invalid Service Account credentials:', error.message);
            return false;
        }
    }

    /**
     * Lấy access token từ Google OAuth2 với caching
     * @param {string} userEmail - Email người dùng cần giả lập
     * @param {boolean} forceRefresh - Bắt buộc refresh token
     * @returns {Promise<string>} Access token
     */
    async getAccessToken(userEmail, forceRefresh = false) {
        const cacheKey = `token_${userEmail}`;

        // Check cache nếu không force refresh
        if (!forceRefresh && this.tokenCache.has(cacheKey)) {
            const cached = this.tokenCache.get(cacheKey);
            if (cached.expires > Date.now()) {
                return cached.token;
            }
        }

        try {
            const auth = new google.auth.GoogleAuth({
                keyFile: this.keyPath,
                scopes: this.defaultScopes,
                subject: userEmail // Domain-wide delegation
            });

            const authClient = await auth.getClient();
            const accessTokenResponse = await authClient.getAccessToken();

            // Cache token với expiry time
            this.tokenCache.set(cacheKey, {
                token: accessTokenResponse.token,
                expires: Date.now() + (50 * 60 * 1000) // 50 phút (token có hiệu lực 1h)
            });

            return accessTokenResponse.token;
        } catch (error) {
            console.error('❌ Error getting access token:', error);
            throw new Error(`Failed to get access token for ${userEmail}: ${error.message}`);
        }
    }

    /**
     * Tạo authenticated Google Drive client với caching
     * @param {string} userEmail - Email người dùng cần giả lập
     * @param {string[]} scopes - Custom scopes (optional)
     * @returns {Promise<object>} Google Drive API client
     */
    async getDriveClient(userEmail, scopes = null) {
        const cacheKey = `client_${userEmail}`;

        // Check cache
        if (this.authClientCache.has(cacheKey)) {
            const cached = this.authClientCache.get(cacheKey);
            if (cached.expires > Date.now()) {
                return cached.client;
            }
        }

        try {
            const scopesToUse = scopes || this.defaultScopes;

            const auth = new google.auth.GoogleAuth({
                keyFile: this.keyPath,
                scopes: scopesToUse,
                subject: userEmail
            });

            const authClient = await auth.getClient();
            const driveClient = google.drive({ version: 'v3', auth: authClient });

            // Cache client
            this.authClientCache.set(cacheKey, {
                client: driveClient,
                expires: Date.now() + (50 * 60 * 1000) // 50 phút
            });

            return driveClient;
        } catch (error) {
            console.error('❌ Error creating Drive client:', error);
            throw new Error(`Failed to create Drive client for ${userEmail}: ${error.message}`);
        }
    }

    /**
     * Tạo authenticated Admin Directory client
     * @param {string} userEmail - Email admin cần giả lập
     * @returns {Promise<object>} Admin Directory API client
     */
    async getAdminClient(userEmail) {
        try {
            const auth = new google.auth.GoogleAuth({
                keyFile: this.keyPath,
                scopes: ['https://www.googleapis.com/auth/admin.directory.user.readonly'],
                subject: userEmail
            });

            const authClient = await auth.getClient();
            return google.admin({ version: 'directory_v1', auth: authClient });
        } catch (error) {
            console.error('❌ Error creating Admin client:', error);
            throw new Error(`Failed to create Admin client for ${userEmail}: ${error.message}`);
        }
    }

    /**
     * Kiểm tra kết nối và quyền truy cập
     * @param {string} userEmail - Email người dùng để test
     * @returns {Promise<object>} Test result với details
     */
    async testConnection(userEmail) {
        const result = {
            success: false,
            userEmail: userEmail,
            connectedAs: null,
            permissions: [],
            errors: []
        };

        try {
            // Test Drive API connection
            console.log(`🔍 Testing Google Drive connection for: ${userEmail}`);

            const drive = await this.getDriveClient(userEmail);
            const aboutResponse = await drive.about.get({
                fields: 'user,storageQuota,canCreateDrives'
            });

            result.connectedAs = aboutResponse.data.user.emailAddress;
            result.storageQuota = aboutResponse.data.storageQuota;
            result.canCreateDrives = aboutResponse.data.canCreateDrives;

            // Test basic Drive operations
            const filesResponse = await drive.files.list({
                pageSize: 1,
                fields: 'files(id,name)'
            });

            result.permissions.push('drive.files.list');

            // Test permissions API
            try {
                const testFile = filesResponse.data.files?.[0];
                if (testFile) {
                    await drive.permissions.list({
                        fileId: testFile.id,
                        fields: 'permissions(id,type,role,emailAddress)'
                    });
                    result.permissions.push('drive.permissions.list');
                }
            } catch (permError) {
                result.errors.push(`Permissions API: ${permError.message}`);
            }

            result.success = true;
            console.log(`✅ Successfully connected as: ${result.connectedAs}`);
            console.log(`✅ Available permissions: ${result.permissions.join(', ')}`);

        } catch (error) {
            result.errors.push(`Drive API: ${error.message}`);
            console.error('❌ Drive connection test failed:', error.message);
        }

        // Test Admin Directory API (optional)
        try {
            const admin = await this.getAdminClient(userEmail);
            await admin.users.get({ userKey: userEmail });
            result.permissions.push('admin.directory.users.readonly');
            console.log('✅ Admin Directory API accessible');
        } catch (adminError) {
            result.errors.push(`Admin API: ${adminError.message}`);
            console.log('⚠️ Admin Directory API not accessible (optional)');
        }

        return result;
    }

    /**
     * Clear all caches
     */
    clearCache() {
        this.authClientCache.clear();
        this.tokenCache.clear();
        console.log('🧹 Auth cache cleared');
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            authClients: this.authClientCache.size,
            tokens: this.tokenCache.size,
            authClientKeys: Array.from(this.authClientCache.keys()),
            tokenKeys: Array.from(this.tokenCache.keys())
        };
    }

    /**
     * Validate Domain-wide Delegation setup
     * @param {string} adminEmail - Email của admin để test
     * @returns {Promise<object>} Validation result
     */
    async validateDomainWideDelegation(adminEmail) {
        const result = {
            success: false,
            serviceAccount: this.serviceAccountEmail,
            adminEmail: adminEmail,
            delegationWorking: false,
            errors: []
        };

        try {
            // Test với admin email
            const adminTest = await this.testConnection(adminEmail);

            if (adminTest.success) {
                result.delegationWorking = true;
                result.success = true;
                console.log('✅ Domain-wide delegation is working correctly');
            } else {
                result.errors = adminTest.errors;
                console.log('❌ Domain-wide delegation test failed');
            }

        } catch (error) {
            result.errors.push(`Delegation test: ${error.message}`);
            console.error('❌ Domain-wide delegation validation failed:', error.message);
        }

        return result;
    }
}

// Export singleton instance
export const googleAuth = new GoogleAuth();