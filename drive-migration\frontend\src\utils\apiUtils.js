// API utility functions for better error handling

/**
 * Enhanced fetch wrapper with better error handling
 * @param {string} url - API endpoint URL
 * @param {object} options - Fetch options
 * @returns {Promise<object>} - Response data or throws enhanced error
 */
export const apiCall = async (url, options = {}) => {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    // Check if response is ok
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText };
      }

      const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      error.status = response.status;
      error.statusText = response.statusText;
      error.response = {
        status: response.status,
        statusText: response.statusText,
        data: errorData
      };
      
      throw error;
    }

    // Try to parse JSON response
    try {
      return await response.json();
    } catch (e) {
      // If response is not JSON, return text
      return await response.text();
    }
  } catch (error) {
    // Network errors or other fetch errors
    if (!error.status) {
      error.message = `Lỗi kết nối: ${error.message}`;
    }
    throw error;
  }
};

/**
 * GET request wrapper
 * @param {string} url - API endpoint URL
 * @param {object} options - Additional fetch options
 * @returns {Promise<object>} - Response data
 */
export const apiGet = (url, options = {}) => {
  return apiCall(url, { method: 'GET', ...options });
};

/**
 * POST request wrapper
 * @param {string} url - API endpoint URL
 * @param {object} data - Request body data
 * @param {object} options - Additional fetch options
 * @returns {Promise<object>} - Response data
 */
export const apiPost = (url, data = null, options = {}) => {
  return apiCall(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : null,
    ...options
  });
};

/**
 * PUT request wrapper
 * @param {string} url - API endpoint URL
 * @param {object} data - Request body data
 * @param {object} options - Additional fetch options
 * @returns {Promise<object>} - Response data
 */
export const apiPut = (url, data = null, options = {}) => {
  return apiCall(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : null,
    ...options
  });
};

/**
 * DELETE request wrapper
 * @param {string} url - API endpoint URL
 * @param {object} options - Additional fetch options
 * @returns {Promise<object>} - Response data
 */
export const apiDelete = (url, options = {}) => {
  return apiCall(url, { method: 'DELETE', ...options });
};

/**
 * Format error for display
 * @param {Error} error - Error object
 * @returns {object} - Formatted error info
 */
export const formatError = (error) => {
  if (typeof error === 'string') {
    return { message: error, details: null, code: null };
  }
  
  if (error instanceof Error) {
    return { 
      message: error.message, 
      details: error.stack, 
      code: error.code || error.status || null 
    };
  }
  
  // Handle API error responses
  if (error.response) {
    return {
      message: error.response.data?.message || error.response.statusText || 'Lỗi API',
      details: error.response.data?.details || `HTTP ${error.response.status}`,
      code: error.response.status
    };
  }
  
  return { 
    message: error.message || 'Lỗi không xác định', 
    details: JSON.stringify(error, null, 2),
    code: null 
  };
};

/**
 * Create a retry function for API calls
 * @param {Function} apiFunction - API function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} delay - Delay between retries in ms
 * @returns {Function} - Retry wrapper function
 */
export const createRetryWrapper = (apiFunction, maxRetries = 3, delay = 1000) => {
  return async (...args) => {
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiFunction(...args);
      } catch (error) {
        lastError = error;
        
        // Don't retry on client errors (4xx)
        if (error.status >= 400 && error.status < 500) {
          throw error;
        }
        
        // Don't retry on last attempt
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, delay * (attempt + 1)));
      }
    }
    
    throw lastError;
  };
};
