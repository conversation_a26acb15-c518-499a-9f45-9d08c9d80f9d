/* Drive-to-Lark Migrator - Main Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.user-info {
  margin-top: 1rem;
}

.user-email-input {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  width: 300px;
  max-width: 100%;
}

/* Main Content */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Step Indicator */
.step-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  gap: 1rem;
}

.step {
  padding: 0.75rem 1.5rem;
  background: #e2e8f0;
  color: #64748b;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.step.active {
  background: #3b82f6;
  color: white;
  transform: scale(1.05);
}

.step.completed {
  background: #10b981;
  color: white;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Cards */
.card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

/* Scope Selector */
.scope-selector {
  max-width: 800px;
  margin: 0 auto;
}

.scope-selector h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #1f2937;
}

.scope-options {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.scope-option {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.scope-option:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.radio-label {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  cursor: pointer;
  gap: 1rem;
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
}

.radio-label input[type="radio"]:checked+.radio-custom {
  border-color: #3b82f6;
  background: #3b82f6;
}

.radio-label input[type="radio"]:checked+.radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.option-content h3 {
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.option-content p {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Form Elements */
.option-group {
  margin-bottom: 1.5rem;
}

.option-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.number-input,
.search-input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  margin-left: 0.5rem;
  width: 80px;
}

.search-input {
  width: 300px;
  margin-left: 0;
}

/* Checkboxes */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked+.checkbox-custom {
  border-color: #3b82f6;
  background: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked+.checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
}

/* Loading States */
.loading-state {
  text-align: center;
  padding: 3rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Error States */
.error-state {
  text-align: center;
  padding: 2rem;
}

.error-message {
  color: #dc2626;
  margin-bottom: 1rem;
}

/* Validation Messages */
.validation-message {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.validation-message p {
  color: #92400e;
  margin-bottom: 0.5rem;
}

.validation-message p:last-child {
  margin-bottom: 0;
}

/* Action Buttons */
.action-buttons {
  text-align: center;
  margin-top: 2rem;
}

/* Folder Browser */
.folder-browser {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.browser-header {
  background: #f9fafb;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.breadcrumb-link {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.breadcrumb-link:hover:not(:disabled) {
  background: #dbeafe;
}

.breadcrumb-link.current {
  color: #374151;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #6b7280;
  margin: 0 0.25rem;
}

.browser-actions {
  display: flex;
  gap: 0.5rem;
}

.folder-list {
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
}

.folders-grid {
  display: grid;
  gap: 1px;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  gap: 1rem;
}

.folder-item:hover {
  background: #f3f4f6;
}

.folder-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.folder-info {
  flex: 1;
  min-width: 0;
}

.folder-name {
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.folder-meta {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.folder-arrow {
  color: #9ca3af;
  flex-shrink: 0;
}

.browser-footer {
  background: #f9fafb;
  padding: 0.75rem 1rem;
  border-top: 1px solid #e5e7eb;
  font-size: 0.875rem;
  color: #6b7280;
}

.selected-folder {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  margin-top: 1rem;
}

.folder-path {
  flex: 1;
  font-family: monospace;
  color: #0f172a;
}

/* Scan Progress */
.scan-progress {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.progress-overview {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.progress-bar-container {
  margin-bottom: 2rem;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease;
  border-radius: 6px;
}

.progress-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.scan-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.scan-details {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: #374151;
}

.detail-value {
  color: #6b7280;
}

.status-running {
  color: #3b82f6;
}

.status-completed {
  color: #10b981;
}

.status-failed {
  color: #dc2626;
}

.status-cancelled {
  color: #f59e0b;
}

.scan-summary,
.scan-error {
  margin-top: 2rem;
}

.summary-card,
.error-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.summary-card {
  border-left: 4px solid #10b981;
}

.error-card {
  border-left: 4px solid #dc2626;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }

  .step-indicator {
    flex-direction: column;
    align-items: center;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .user-email-input {
    width: 100%;
  }

  .browser-header {
    flex-direction: column;
    align-items: stretch;
  }

  .scan-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* File List */
.file-list {
  max-width: 1200px;
  margin: 0 auto;
}

.file-filters {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  min-width: 150px;
}

.selection-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.selection-info {
  display: flex;
  gap: 2rem;
  font-size: 0.875rem;
  color: #374151;
}

.select-all-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
}

.files-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.table-header {
  display: grid;
  grid-template-columns: 60px 60px 1fr 100px 120px 200px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
}

.table-header>div {
  padding: 1rem 0.75rem;
  border-right: 1px solid #e5e7eb;
}

.table-header>div:last-child {
  border-right: none;
}

.table-body {
  max-height: 600px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 60px 60px 1fr 100px 120px 200px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f9fafb;
}

.table-row>div {
  padding: 0.75rem;
  border-right: 1px solid #f3f4f6;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.table-row>div:last-child {
  border-right: none;
}

.col-select {
  justify-content: center;
}

.col-icon {
  justify-content: center;
}

.file-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.file-checkbox input[type="checkbox"] {
  display: none;
}

.file-icon {
  font-size: 1.25rem;
}

.file-name {
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-path {
  font-family: monospace;
  font-size: 0.75rem;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.page-info {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Empty and Info States */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.info-message {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 8px;
  margin: 1rem 0;
}

/* Migration Step */
.migration-step {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.migration-step h2 {
  margin-bottom: 1rem;
  color: #1f2937;
}

.migration-step p {
  margin-bottom: 2rem;
  color: #6b7280;
  font-size: 1.125rem;
}

/* Additional responsive styles for file list */
@media (max-width: 1024px) {

  .table-header,
  .table-row {
    grid-template-columns: 50px 50px 1fr 80px 100px;
  }

  .col-path {
    display: none;
  }
}

@media (max-width: 768px) {

  .table-header,
  .table-row {
    grid-template-columns: 40px 40px 1fr 80px;
  }

  .col-modified {
    display: none;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-select {
    min-width: auto;
  }

  .selection-summary {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
}

/* Error Display Component */
.error-display {
  margin: 1rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.1);
  background: white;
  border: 1px solid #fecaca;
}

.error-content {
  padding: 0;
}

.error-header {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.error-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.error-title {
  font-weight: 600;
  font-size: 1.125rem;
  flex: 1;
}

.error-dismiss {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

.error-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.error-body {
  padding: 1.5rem;
}

.error-message {
  font-size: 1rem;
  color: #374151;
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.error-code {
  font-size: 0.875rem;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f3f4f6;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border-left: 3px solid #dc2626;
}

.error-details {
  margin-top: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.error-details summary {
  background: #f9fafb;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-weight: 500;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;
}

.error-details summary:hover {
  background: #f3f4f6;
}

.error-details-content {
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-actions {
  padding: 0 1.5rem 1.5rem;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

/* Error display variants */
.error-display.inline {
  margin: 0.5rem 0;
  border-radius: 6px;
}

.error-display.compact .error-header {
  padding: 0.75rem 1rem;
}

.error-display.compact .error-body {
  padding: 1rem;
}

.error-display.compact .error-actions {
  padding: 0 1rem 1rem;
}